{"name": "sidashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@portabletext/react": "^3.2.1", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@reduxjs/toolkit": "^2.7.0", "@sanity/image-url": "^1.1.0", "@sanity/types": "^3.79.0", "@sanity/vision": "^2.2.6", "@tailwindcss/container-queries": "^0.1.1", "@tanstack/react-query": "^5.74.4", "@tanstack/react-table": "^8.21.3", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.6.0", "ethers": "5.7", "get-video-id": "^4.1.7", "install": "^0.13.0", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.486.0", "moment": "^2.30.1", "next": "14.2.15", "next-sanity": "^0.8.5", "next-themes": "^0.4.6", "npm": "^11.4.1", "pino-pretty": "^13.0.0", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.55.0", "react-iframe": "^1.8.5", "react-redux": "^9.2.0", "sonner": "^2.0.3", "tailwind-merge": "^3.1.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "viem": "^2.21.57", "wagmi": "^2.14.4", "yup": "^1.6.1", "zod": "^3.24.2"}, "devDependencies": {"@tailwindcss/typography": "^0.5.15", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.15", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}