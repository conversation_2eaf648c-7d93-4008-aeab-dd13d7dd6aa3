<svg width="1032" height="268" viewBox="0 0 1032 268" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clipPath="url(#clip0_894_5574)">
<rect y="0.117188" width="1032" height="267" rx="12" fill="#E9E9E9"/>
<g filter="url(#filter0_f_894_5574)">
<rect x="-90.0703" y="124.549" width="722.76" height="517.56" rx="16" fill="url(#paint0_linear_894_5574)"/>
</g>
<g filter="url(#filter1_f_894_5574)">
<rect x="257.25" y="-198.451" width="722.76" height="517.56" rx="16" fill="url(#paint1_linear_894_5574)"/>
</g>
<g filter="url(#filter2_f_894_5574)">
<rect x="524.01" y="158.748" width="722.76" height="517.56" rx="16" fill="url(#paint2_linear_894_5574)"/>
</g>
<g style="mix-blend-mode:multiply" filter="url(#filter3_f_894_5574)">
<rect x="644.85" y="245.389" width="481.08" height="344.28" rx="16" fill="url(#paint3_linear_894_5574)"/>
</g>
<g style="mix-blend-mode:multiply" filter="url(#filter4_f_894_5574)">
<rect x="-100.711" y="455.148" width="262.2" height="187.72" rx="16" fill="url(#paint4_linear_894_5574)"/>
</g>
<foreignObject x="455.486" y="-484.018" width="856.598" height="856.598"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2px);clipPath:url(#bgblur_1_894_5574_clip_path);height:100%;width:100%"></div></foreignObject><g data-figma-bg-blur-radius="4">
<circle cx="883.786" cy="-55.7183" r="424.299" fill="white" fill-opacity="0.13"/>
<circle cx="883.786" cy="-55.7183" r="423.799" stroke="white" stroke-opacity="0.42"/>
</g>
<foreignObject x="458.695" y="-540.701" width="856.598" height="856.598"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2px);clipPath:url(#bgblur_2_894_5574_clip_path);height:100%;width:100%"></div></foreignObject><g data-figma-bg-blur-radius="4">
<circle cx="886.995" cy="-112.402" r="424.299" fill="white" fill-opacity="0.13"/>
<circle cx="886.995" cy="-112.402" r="423.799" stroke="white" stroke-opacity="0.42"/>
</g>
<foreignObject x="455.486" y="-598.072" width="856.598" height="856.598"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2px);clipPath:url(#bgblur_3_894_5574_clip_path);height:100%;width:100%"></div></foreignObject><g data-figma-bg-blur-radius="4">
<circle cx="883.786" cy="-169.773" r="424.299" fill="white" fill-opacity="0.13"/>
<circle cx="883.786" cy="-169.773" r="423.799" stroke="white" stroke-opacity="0.42"/>
</g>
</g>
<defs>
<filter id="filter0_f_894_5574" x="-419.87" y="-205.251" width="1382.36" height="1177.16" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
<feFlood floodOpacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="164.9" result="effect1_foregroundBlur_894_5574"/>
</filter>
<filter id="filter1_f_894_5574" x="-72.55" y="-528.251" width="1382.36" height="1177.16" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
<feFlood floodOpacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="164.9" result="effect1_foregroundBlur_894_5574"/>
</filter>
<filter id="filter2_f_894_5574" x="305.01" y="-60.252" width="1160.76" height="955.561" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
<feFlood floodOpacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="109.5" result="effect1_foregroundBlur_894_5574"/>
</filter>
<filter id="filter3_f_894_5574" x="425.85" y="26.3887" width="919.08" height="782.279" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
<feFlood floodOpacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="109.5" result="effect1_foregroundBlur_894_5574"/>
</filter>
<filter id="filter4_f_894_5574" x="-319.711" y="236.148" width="700.199" height="625.721" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
<feFlood floodOpacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="109.5" result="effect1_foregroundBlur_894_5574"/>
</filter>
<clipPath id="bgblur_1_894_5574_clip_path" transform="translate(-455.486 484.018)"><circle cx="883.786" cy="-55.7183" r="424.299"/>
</clipPath><clipPath id="bgblur_2_894_5574_clip_path" transform="translate(-458.695 540.701)"><circle cx="886.995" cy="-112.402" r="424.299"/>
</clipPath><clipPath id="bgblur_3_894_5574_clip_path" transform="translate(-455.486 598.072)"><circle cx="883.786" cy="-169.773" r="424.299"/>
</clipPath><linearGradient id="paint0_linear_894_5574" x1="-90.0703" y1="145.558" x2="576.653" y2="631.836" gradientUnits="userSpaceOnUse">
<stop offset="0.00869483" stopColor="#B3BFFF"/>
<stop offset="1" stopColor="#FADCD8"/>
</linearGradient>
<linearGradient id="paint1_linear_894_5574" x1="257.25" y1="-177.442" x2="923.973" y2="308.836" gradientUnits="userSpaceOnUse">
<stop offset="0.00869483" stopColor="#B3BFFF"/>
<stop offset="1" stopColor="#FADCD8"/>
</linearGradient>
<linearGradient id="paint2_linear_894_5574" x1="524.01" y1="179.757" x2="1190.73" y2="666.035" gradientUnits="userSpaceOnUse">
<stop offset="0.00869483" stopColor="#B3BFFF"/>
<stop offset="1" stopColor="#FADCD8"/>
</linearGradient>
<linearGradient id="paint3_linear_894_5574" x1="644.85" y1="259.364" x2="1088.44" y2="583.1" gradientUnits="userSpaceOnUse">
<stop offset="0.00869483" stopColor="#B3BFFF"/>
<stop offset="1" stopColor="#FADCD8"/>
</linearGradient>
<linearGradient id="paint4_linear_894_5574" x1="-100.711" y1="462.768" x2="141.126" y2="639.19" gradientUnits="userSpaceOnUse">
<stop offset="0.00869483" stopColor="#B3BFFF"/>
<stop offset="1" stopColor="#FADCD8"/>
</linearGradient>
<clipPath id="clip0_894_5574">
<rect y="0.117188" width="1032" height="267" rx="12" fill="white"/>
</clipPath>
</defs>
</svg>
