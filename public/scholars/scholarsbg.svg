<svg width="1000" height="221" viewBox="0 0 1000 221" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clipPath="url(#clip0_894_7083)">
<rect x="0.648438" y="0.171875" width="999" height="220" rx="10" fill="white"/>
<g filter="url(#filter0_f_894_7083)">
<rect x="-89.543" y="124.617" width="722.76" height="517.56" rx="16" fill="url(#paint0_linear_894_7083)"/>
</g>
<g filter="url(#filter1_f_894_7083)">
<rect x="257.777" y="-198.383" width="722.76" height="517.56" rx="16" fill="url(#paint1_linear_894_7083)"/>
</g>
<g filter="url(#filter2_f_894_7083)">
<rect x="524.537" y="158.816" width="722.76" height="517.56" rx="16" fill="url(#paint2_linear_894_7083)"/>
</g>
<g style="mix-blend-mode:multiply" filter="url(#filter3_f_894_7083)">
<rect x="645.377" y="245.457" width="481.08" height="344.28" rx="16" fill="url(#paint3_linear_894_7083)"/>
</g>
</g>
<defs>
<filter id="filter0_f_894_7083" x="-419.343" y="-205.183" width="1382.36" height="1177.16" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
<feFlood floodOpacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="164.9" result="effect1_foregroundBlur_894_7083"/>
</filter>
<filter id="filter1_f_894_7083" x="-72.0226" y="-528.183" width="1382.36" height="1177.16" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
<feFlood floodOpacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="164.9" result="effect1_foregroundBlur_894_7083"/>
</filter>
<filter id="filter2_f_894_7083" x="305.537" y="-60.1836" width="1160.76" height="955.561" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
<feFlood floodOpacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="109.5" result="effect1_foregroundBlur_894_7083"/>
</filter>
<filter id="filter3_f_894_7083" x="426.377" y="26.457" width="919.08" height="782.279" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
<feFlood floodOpacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="109.5" result="effect1_foregroundBlur_894_7083"/>
</filter>
<linearGradient id="paint0_linear_894_7083" x1="-89.543" y1="145.626" x2="577.18" y2="631.905" gradientUnits="userSpaceOnUse">
<stop offset="0.00869483" stopColor="#B3BFFF"/>
<stop offset="1" stopColor="#FADCD8"/>
</linearGradient>
<linearGradient id="paint1_linear_894_7083" x1="257.777" y1="-177.374" x2="924.5" y2="308.905" gradientUnits="userSpaceOnUse">
<stop offset="0.00869483" stopColor="#B3BFFF"/>
<stop offset="1" stopColor="#FADCD8"/>
</linearGradient>
<linearGradient id="paint2_linear_894_7083" x1="524.537" y1="179.825" x2="1191.26" y2="666.104" gradientUnits="userSpaceOnUse">
<stop offset="0.00869483" stopColor="#B3BFFF"/>
<stop offset="1" stopColor="#FADCD8"/>
</linearGradient>
<linearGradient id="paint3_linear_894_7083" x1="645.377" y1="259.432" x2="1088.96" y2="583.168" gradientUnits="userSpaceOnUse">
<stop offset="0.00869483" stopColor="#B3BFFF"/>
<stop offset="1" stopColor="#FADCD8"/>
</linearGradient>
<clipPath id="clip0_894_7083">
<rect x="0.648438" y="0.171875" width="999" height="220" rx="10" fill="white"/>
</clipPath>
</defs>
</svg>
