export function IdeasLab() {
  return (
    <>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="25"
        height="25"
        viewBox="0 0 25 25"
        fill="none"
      >
        <path
          d="M6.7925 15.4707C6.41409 14.6201 6.20312 13.6738 6.20312 12.6766C6.20312 8.97303 9.11328 5.9707 12.7031 5.9707C16.293 5.9707 19.2031 8.97303 19.2031 12.6766C19.2031 13.6738 18.9921 14.6201 18.6137 15.4707"
          stroke="#9F44D3"
          stroke-width="1.5"
          stroke-linecap="round"
        />
        <path
          d="M12.7031 2.4707V3.4707"
          stroke="#9F44D3"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M22.7031 12.4707H21.7031"
          stroke="#9F44D3"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M3.70312 12.4707H2.70312"
          stroke="#9F44D3"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M19.7735 5.39844L19.0664 6.10555"
          stroke="#9F44D3"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M6.33992 6.1075L5.63281 5.40039"
          stroke="#9F44D3"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M15.2206 19.7761C16.231 19.4493 16.6362 18.5245 16.7502 17.5943C16.7842 17.3164 16.5556 17.0859 16.2756 17.0859L9.18047 17.0861C8.89087 17.0861 8.65829 17.3319 8.69287 17.6194C8.80452 18.5478 9.08632 19.226 10.1571 19.7761M15.2206 19.7761C15.2206 19.7761 10.3333 19.7761 10.1571 19.7761M15.2206 19.7761C15.0991 21.7211 14.5374 22.4914 12.7104 22.4698C10.7562 22.5059 10.3066 21.5538 10.1571 19.7761"
          stroke="#9F44D3"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </>
  );
}
