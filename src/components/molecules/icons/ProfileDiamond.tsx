export function ProfileDiamond() {
  return (
    <>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="51"
        height="47"
        viewBox="0 0 51 47"
        fill="none"
      >
        <path
          d="M38.1805 22.6805L25.4805 9.98047V22.6805H38.1805Z"
          fill="#FA29A1"
        />
        <path
          opacity="0.25"
          d="M32.1979 22.6295L32.1958 22.6291C28.8073 22.07 26.152 19.414 25.5938 16.0254V22.6293L32.1979 22.6295Z"
          fill="#FDFFD2"
        />
        <path
          d="M12.7812 22.6797L25.481 35.3797V22.6797H12.7812Z"
          fill="#E81C82"
        />
        <path
          opacity="0.25"
          d="M14.0508 23.6668L25.4973 35.2087L25.3388 22.8262L19.5714 23.028L21.6216 23.3101C23.3083 23.5421 24.5242 25.0449 24.3992 26.7428L24.0164 31.944L14.0508 23.6668Z"
          fill="#FDFFD2"
        />
        <path
          d="M25.481 9.98047L12.7812 22.6805H25.481V9.98047Z"
          fill="url(#paint0_linear_894_7448)"
        />
        <path
          opacity="0.25"
          d="M25.4812 9.98047L23.6182 16.9076C23.0979 18.8427 21.5824 20.3518 19.6452 20.8643L12.7812 22.6803H25.4812V9.98047Z"
          fill="#FDFFD2"
        />
        <path
          d="M25.4805 22.6797V35.3797L38.1805 22.6797H25.4805Z"
          fill="url(#paint1_linear_894_7448)"
        />
        <path
          opacity="0.25"
          d="M25.4512 11.5938V12.9981L35.9848 22.5231L25.4512 11.5938Z"
          fill="#FDFFD2"
        />
        <path
          d="M26.5059 11.9043L37.6881 22.6813L28.4545 31.2875L36.7954 22.6851L26.5059 11.9043Z"
          fill="url(#paint2_linear_894_7448)"
        />
        <path
          opacity="0.25"
          d="M25.9004 23.1294L34.9424 25.5946L37.9094 22.8242L25.9004 23.1294Z"
          fill="#FDFFD2"
        />
        <path
          opacity="0.5"
          d="M25.1366 14.3887L25.0022 22.143L17.5391 22.2954L24.3901 21.3991L25.1366 14.3887Z"
          fill="#FFE6E9"
        />
        <path
          opacity="0.5"
          d="M22.1914 23.0059H25.0826L25.1386 33.6177L24.6678 23.5213L22.1914 23.0059Z"
          fill="#FFE6E9"
        />
        <path
          opacity="0.5"
          d="M13.4844 22.2942C13.4844 22.2942 21.556 16.2335 23.1791 13.0155L23.2559 12.8633L13.4844 22.2942Z"
          fill="#FFE6E9"
        />
        <path
          opacity="0.25"
          d="M25.9004 33.1146L33.6997 25.5938L26.4084 29.8819L25.9004 33.1146Z"
          fill="#FDFFD2"
        />
        <path
          d="M25.4817 25.5938C25.5942 24.696 25.6459 23.7984 25.6862 22.9006C26.2256 22.9169 26.765 22.9296 27.3044 22.9309C27.9121 22.9358 28.5195 22.9288 29.1272 22.915C29.7349 22.9008 30.3423 22.8767 30.95 22.8402C31.5576 22.8042 32.1653 22.7556 32.7727 22.6804C32.1651 22.6051 31.5574 22.5566 30.95 22.5206C30.3423 22.4839 29.7349 22.46 29.1272 22.4458C28.5195 22.4318 27.9119 22.4248 27.3044 22.4299C26.7811 22.4311 26.2578 22.4434 25.7345 22.4589C25.7299 20.4161 25.7189 18.3734 25.6799 16.3306C25.6627 15.2723 25.6358 14.2139 25.6061 13.1556C25.5669 12.1745 25.5406 11.1934 25.4914 10.2125C25.9344 10.6302 26.3772 11.0482 26.8273 11.4587C27.318 11.9093 27.8055 12.3631 28.3053 12.8046C27.8638 12.3048 27.41 11.8173 26.9594 11.3267C26.5105 10.8343 26.0535 10.35 25.5966 9.86572L25.4874 9.75L25.3664 9.86572L23.7424 11.4168L22.1346 12.9837C21.0697 14.0356 20.0087 15.0911 18.9583 16.1574C16.8564 18.289 14.7842 20.4498 12.7817 22.6808C15.0127 20.6785 17.1735 18.6061 19.3051 16.5042C20.3714 15.4538 21.4269 14.3927 22.4786 13.3279L24.0455 11.7201L24.8162 10.9132C25.0434 10.6754 25.4423 10.846 25.4293 11.1747C25.4037 11.8352 25.3831 12.4956 25.3568 13.156C25.3272 14.2143 25.3003 15.2728 25.2831 16.331C25.2492 18.1137 25.2634 19.8966 25.2661 21.6794C25.2668 22.1136 24.9139 22.4665 24.4796 22.4659C22.6969 22.4636 20.914 22.4494 19.1313 22.4835C18.073 22.5004 17.0145 22.5274 15.9563 22.5572C14.898 22.5992 13.8395 22.6238 12.7812 22.681C13.8395 22.7383 14.898 22.7628 15.9563 22.8048C17.0145 22.8345 18.073 22.8614 19.1313 22.8783C21.1802 22.9176 23.2293 22.9286 25.2782 22.9339C25.3187 23.82 25.3706 24.707 25.4817 25.5938Z"
          fill="white"
        />
        <path
          d="M34.9536 25.8011C33.8846 26.8485 32.8115 27.8922 31.7526 28.95L28.5687 32.1161L25.4988 35.208C25.5853 34.2278 25.6058 32.5317 25.6092 31.6445C25.6052 30.7032 25.5821 29.762 25.4822 28.8207C25.3824 29.762 25.3593 30.7032 25.3553 31.6445C25.358 32.3611 25.372 33.605 25.4229 34.5736C25.4346 34.7968 25.1656 34.9174 25.0056 34.7612L22.7011 32.5093C21.7458 31.5809 20.7922 30.6411 19.8262 29.7234C20.7477 30.6859 21.6689 31.6581 22.601 32.6097L25.4051 35.4573L25.4814 35.5348L25.5596 35.4571L28.7458 32.2933L31.9119 29.1094C32.9698 28.0507 34.0132 26.9774 35.0609 25.9083C36.1098 24.8407 37.1473 23.7615 38.1823 22.6797C37.1005 23.7146 36.0214 24.7521 34.9536 25.8011Z"
          fill="white"
        />
        <g filter="url(#filter0_f_894_7448)">
          <path
            d="M28.6696 24.3745C26.1207 29.3953 20.7185 31.7721 20.7185 31.7721C20.7185 31.7721 19.449 26.0084 21.9977 20.9875C24.5466 15.9666 29.9487 13.5898 29.9487 13.5898C29.9487 13.5898 31.2183 19.3536 28.6696 24.3745Z"
            fill="white"
          />
        </g>
        <g filter="url(#filter1_f_894_7448)">
          <path
            d="M20.1772 19.0457C17.9725 20.6181 15.1406 20.428 15.1406 20.428C15.1406 20.428 15.8831 17.6886 18.0878 16.1161C20.2925 14.5436 23.1243 14.7338 23.1243 14.7338C23.1243 14.7338 22.3817 17.4732 20.1772 19.0457Z"
            fill="white"
          />
        </g>
        <g filter="url(#filter2_f_894_7448)">
          <path
            d="M33.1382 30.1898C31.0987 31.971 28.2617 32.0601 28.2617 32.0601C28.2617 32.0601 28.7316 29.2608 30.7712 27.4796C32.8108 25.6984 35.6477 25.6094 35.6477 25.6094C35.6477 25.6094 35.1776 28.4084 33.1382 30.1898Z"
            fill="white"
          />
        </g>
        <path
          d="M24.4942 15.701C24.4264 15.6863 24.3736 15.6336 24.359 15.5657L24.2683 13.8125L24.1776 15.5657C24.163 15.6336 24.1102 15.6863 24.0424 15.701L22.6367 15.7917L24.0424 15.8824C24.1102 15.897 24.163 15.9498 24.1776 16.0176L24.2683 17.7709L24.359 16.0176C24.3736 15.9498 24.4264 15.897 24.4942 15.8824L25.8999 15.7917L24.4942 15.701Z"
          fill="white"
        />
        <path
          d="M29.8658 22.3517C29.8228 22.3818 29.7662 22.3839 29.7211 22.357L28.7012 21.5039L29.6276 22.4577C29.6577 22.5007 29.6598 22.5573 29.6329 22.6024L28.9589 23.4293L29.7336 22.6957C29.7766 22.6656 29.8332 22.6635 29.8783 22.6904L30.8982 23.5435L29.9718 22.5897C29.9417 22.5467 29.9396 22.4901 29.9665 22.445L30.6405 21.6181L29.8658 22.3517Z"
          fill="white"
        />
        <defs>
          <filter
            id="filter0_f_894_7448"
            x="14.8023"
            y="7.98984"
            width="21.0633"
            height="29.3816"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="BackgroundImageFix"
              result="shape"
            />
            <feGaussianBlur
              stdDeviation="2.8"
              result="effect1_foregroundBlur_894_7448"
            />
          </filter>
          <filter
            id="filter1_f_894_7448"
            x="0.740625"
            y="0.326563"
            width="36.7844"
            height="34.509"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="BackgroundImageFix"
              result="shape"
            />
            <feGaussianBlur
              stdDeviation="7.2"
              result="effect1_foregroundBlur_894_7448"
            />
          </filter>
          <filter
            id="filter2_f_894_7448"
            x="13.8617"
            y="11.2094"
            width="36.1867"
            height="35.2512"
            filterUnits="userSpaceOnUse"
            colorInterpolationFilters="sRGB"
          >
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="BackgroundImageFix"
              result="shape"
            />
            <feGaussianBlur
              stdDeviation="7.2"
              result="effect1_foregroundBlur_894_7448"
            />
          </filter>
          <linearGradient
            id="paint0_linear_894_7448"
            x1="19.1311"
            y1="16.3305"
            x2="25.4811"
            y2="22.6805"
            gradientUnits="userSpaceOnUse"
          >
            <stop offset="0.0251" stopColor="#F93BA9" />
            <stop offset="0.257" stopColor="#FA6EC3" />
            <stop offset="0.4941" stopColor="#FB9CD9" />
            <stop offset="0.7057" stopColor="#FBBDE9" />
            <stop offset="0.8815" stopColor="#FCD1F3" />
            <stop offset="1" stopColor="#FCD8F7" />
          </linearGradient>
          <linearGradient
            id="paint1_linear_894_7448"
            x1="25.4806"
            y1="29.0297"
            x2="38.1805"
            y2="29.0297"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#EE0384" />
            <stop offset="1" stopColor="#DE0F7C" />
          </linearGradient>
          <linearGradient
            id="paint2_linear_894_7448"
            x1="26.5058"
            y1="21.5958"
            x2="37.6881"
            y2="21.5958"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#EE0384" />
            <stop offset="0.1605" stopColor="#D00469" />
            <stop offset="0.3302" stopColor="#B90553" />
            <stop offset="0.5125" stopColor="#A90644" />
            <stop offset="0.7144" stopColor="#9F063B" />
            <stop offset="0.9749" stopColor="#9C0638" />
          </linearGradient>
        </defs>
      </svg>
    </>
  );
}
