import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { OptimizedCommentSection } from '../OptimizedCommentSection';
import { optimizedCommentService } from '@/lib/services/optimized-comment.service';

// Mock the comment service
jest.mock('@/lib/services/optimized-comment.service');

// Mock framer-motion to avoid animation issues in tests
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    form: ({ children, ...props }: any) => <form {...props}>{children}</form>,
  },
  AnimatePresence: ({ children }: any) => children,
}));

// Mock Redux store
const mockStore = configureStore({
  reducer: {
    user: (state = { roles: ['scholar'] }) => state,
  },
});

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return (
    <Provider store={mockStore}>
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    </Provider>
  );
};

// Mock data
const mockComments = [
  {
    _id: 'comment-1',
    contentId: 'content-1',
    contentType: 'scholar_ideas_lab' as const,
    content: 'This is a test comment',
    userId: 'user-1',
    user: {
      _id: 'user-1',
      email: '<EMAIL>',
      roles: ['scholar' as const],
      firstName: 'Test',
      lastName: 'User',
    },
    isReply: false,
    replyCount: 1,
    reactions: [],
    likeCount: 5,
    dislikeCount: 1,
    isDeleted: false,
    isEdited: false,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    replies: [
      {
        _id: 'reply-1',
        contentId: 'content-1',
        contentType: 'scholar_ideas_lab' as const,
        content: 'This is a reply',
        userId: 'user-2',
        user: {
          _id: 'user-2',
          email: '<EMAIL>',
          roles: ['scholar' as const],
          firstName: 'Reply',
          lastName: 'User',
        },
        parentCommentId: 'comment-1',
        isReply: true,
        replyCount: 0,
        reactions: [],
        likeCount: 2,
        dislikeCount: 0,
        isDeleted: false,
        isEdited: false,
        createdAt: '2024-01-01T01:00:00Z',
        updatedAt: '2024-01-01T01:00:00Z',
        replies: [],
      },
    ],
  },
];

const mockStats = {
  analytics: {
    totalComments: 2,
    totalReplies: 1,
    totalTopLevel: 1,
    uniqueUserCount: 2,
    latestComment: '2024-01-01T01:00:00Z',
    oldestComment: '2024-01-01T00:00:00Z',
  },
};

const mockThreadedResponse = {
  comments: mockComments,
  pagination: {
    page: 1,
    limit: 20,
    totalPages: 1,
    totalComments: 2,
    hasNextPage: false,
    hasPrevPage: false,
  },
};

describe('OptimizedCommentSection', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock service methods
    (optimizedCommentService.getThreadedComments as jest.Mock).mockResolvedValue(mockThreadedResponse);
    (optimizedCommentService.getCommentStats as jest.Mock).mockResolvedValue(mockStats);
    (optimizedCommentService.createComment as jest.Mock).mockResolvedValue(mockComments[0]);
  });

  const defaultProps = {
    contentId: 'content-1',
    contentType: 'scholar_ideas_lab' as const,
    userRole: 'scholar' as const,
  };

  it('renders comment section with header', async () => {
    render(
      <TestWrapper>
        <OptimizedCommentSection {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Discussion')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Share your thoughts about this content...')).toBeInTheDocument();
  });

  it('displays comments when loaded', async () => {
    render(
      <TestWrapper>
        <OptimizedCommentSection {...defaultProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('This is a test comment')).toBeInTheDocument();
    });

    expect(screen.getByText('Test User')).toBeInTheDocument();
  });

  it('displays comment statistics', async () => {
    render(
      <TestWrapper>
        <OptimizedCommentSection {...defaultProps} showStats={true} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('2 comments')).toBeInTheDocument();
      expect(screen.getByText('2 participants')).toBeInTheDocument();
    });
  });

  it('allows creating new comments', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <OptimizedCommentSection {...defaultProps} />
      </TestWrapper>
    );

    const textarea = screen.getByPlaceholderText('Share your thoughts about this content...');
    const submitButton = screen.getByText('Post Comment');

    await user.type(textarea, 'New test comment');
    await user.click(submitButton);

    expect(optimizedCommentService.createComment).toHaveBeenCalledWith({
      contentId: 'content-1',
      contentType: 'scholar_ideas_lab',
      content: 'New test comment',
    });
  });

  it('shows sort controls when there are multiple comments', async () => {
    render(
      <TestWrapper>
        <OptimizedCommentSection {...defaultProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Sort by:')).toBeInTheDocument();
    });

    const sortSelect = screen.getByDisplayValue('Newest');
    expect(sortSelect).toBeInTheDocument();
  });

  it('can be collapsed and expanded', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <OptimizedCommentSection {...defaultProps} />
      </TestWrapper>
    );

    const collapseButton = screen.getByText('Collapse');
    await user.click(collapseButton);

    expect(screen.getByText('Expand')).toBeInTheDocument();
  });

  it('handles permission denied gracefully', () => {
    render(
      <TestWrapper>
        <OptimizedCommentSection
          {...defaultProps}
          contentType="guide_session"
          userRole="scholar"
        />
      </TestWrapper>
    );

    expect(screen.getByText("You don't have permission to view comments for this content.")).toBeInTheDocument();
  });

  it('displays loading state', () => {
    // Mock loading state
    (optimizedCommentService.getThreadedComments as jest.Mock).mockImplementation(
      () => new Promise(() => {}) // Never resolves
    );

    render(
      <TestWrapper>
        <OptimizedCommentSection {...defaultProps} />
      </TestWrapper>
    );

    expect(screen.getByText('Loading comments...')).toBeInTheDocument();
  });

  it('displays empty state when no comments', async () => {
    (optimizedCommentService.getThreadedComments as jest.Mock).mockResolvedValue({
      comments: [],
      pagination: {
        page: 1,
        limit: 20,
        totalPages: 0,
        totalComments: 0,
        hasNextPage: false,
        hasPrevPage: false,
      },
    });

    render(
      <TestWrapper>
        <OptimizedCommentSection {...defaultProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('No comments yet')).toBeInTheDocument();
      expect(screen.getByText('Be the first to share your thoughts!')).toBeInTheDocument();
    });
  });

  it('handles errors gracefully', async () => {
    (optimizedCommentService.getThreadedComments as jest.Mock).mockRejectedValue(
      new Error('Failed to load comments')
    );

    render(
      <TestWrapper>
        <OptimizedCommentSection {...defaultProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Failed to load comments. Please try again.')).toBeInTheDocument();
    });

    expect(screen.getByText('Retry')).toBeInTheDocument();
  });

  it('sorts comments correctly', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <OptimizedCommentSection {...defaultProps} />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Sort by:')).toBeInTheDocument();
    });

    const sortSelect = screen.getByDisplayValue('Newest');
    await user.selectOptions(sortSelect, 'popular');

    expect(sortSelect).toHaveValue('popular');
  });
});
