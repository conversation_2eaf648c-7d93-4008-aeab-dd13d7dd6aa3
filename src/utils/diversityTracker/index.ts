export const selfIdentityOptions = [
  "Women",
  "Men",
  "Non-Binary",
  "Prefer to self-describe",
];

export const ageRangeOptions = [
  "Under 18",
  "18-24",
  "25-34",
  "35-44",
  "45-54",
  "55-64",
  "65 and older",
];

export const ethnicityOptions = [
  "African American or Black",
  "Asian",
  "Hispanic or Latina",
  "Middle Eastern/North African",
  "Native American/Alaska Native/First Nation",
  "Pacific Islander/Native Hawaiian",
  "White",
  "Prefer to self-describe",
  "Prefer not to answer",
];

export const disabilityOptions = [
  "Attention deficit",
  "Deaf or hard of hearing",
  "Mental health condition",
  "Autism",
  "Blind or visually impaired",
  "Learning disability",
  "Mobility-related disability",
  "Speech-related disability",
  "Other chronic condition (optional)",
  "Other disability (optional)",
  "Prefer not to answer",
];

export const sexualOrientationOptions = [
  "Heterosexual/Straight",
  "Gay or Lesbian",
  "Bisexual",
  "Pansexual",
  "Queer",
  "Other",
];
