import { Toaster } from "sonner";
import type { <PERSON>ada<PERSON> } from "next";

import "./globals.css";

import ReduxProvider from "@/providers/ReduxProvider";
import WalletProvider from "@/providers/WagmiProvider";
import { TanstackClientProvider } from "@/providers/TanstackClientProvider";

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressContentEditableWarning={true}>
      <body
        className={`antialiased w-screen overflow-hidden overflow-y-scroll`}
      >
        <WalletProvider>
          <ReduxProvider>
            <TanstackClientProvider>
              {children}
              <Toaster />
            </TanstackClientProvider>
          </ReduxProvider>
        </WalletProvider>
      </body>
    </html>
  );
}
